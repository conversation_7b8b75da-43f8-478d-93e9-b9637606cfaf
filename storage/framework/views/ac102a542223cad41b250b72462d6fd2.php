<?php $__env->startSection('title', 'Notification Preferences'); ?>

<?php $__env->startSection('content_header'); ?>
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1>Notification Preferences</h1>
            <p class="text-muted">Customize how and when you receive notifications</p>
        </div>
        <div class="d-flex" style="gap: 10px;">
            <button type="button" class="btn btn-outline-secondary" id="resetPreferencesBtn" onclick="fallbackResetPreferences()">
                <i class="fas fa-undo"></i> Reset to Defaults
            </button>
            <button type="button" class="btn btn-outline-info" id="testNotificationBtn" onclick="fallbackTestNotification()">
                <i class="fas fa-bell"></i> Test Notification
            </button>
            <button type="button" class="btn btn-primary" id="savePreferencesBtn" onclick="fallbackSavePreferences()">
                <i class="fas fa-save"></i> Save Preferences
            </button>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('css'); ?>
<style>
.preference-card {
    transition: all 0.3s ease;
}
.preference-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
.channel-toggle {
    margin-bottom: 10px;
}
.global-settings {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}
.notification-type-card {
    border-left: 4px solid #007bff;
}
.notification-type-card.booking { border-left-color: #28a745; }
.notification-type-card.cancellation { border-left-color: #dc3545; }
.notification-type-card.payment { border-left-color: #17a2b8; }
.notification-type-card.review { border-left-color: #ffc107; }
.notification-type-card.system { border-left-color: #6c757d; }
.notification-type-card.marketing { border-left-color: #007bff; }
.notification-type-card.alert { border-left-color: #dc3545; }
.notification-type-card.reminder { border-left-color: #ffc107; }
.notification-type-card.customer_message { border-left-color: #17a2b8; }
.notification-type-card.waiting_list { border-left-color: #007bff; }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    
    <div class="card global-settings mb-4">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-globe mr-2"></i>
                Global Notification Settings
            </h3>
            <div class="card-tools">
                <button type="button" class="btn btn-tool text-white" data-card-widget="collapse">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h5>Delivery Channels</h5>
                    <div class="form-group">
                        <div class="custom-control custom-switch channel-toggle">
                            <input type="checkbox" class="custom-control-input" id="globalEmailEnabled">
                            <label class="custom-control-label" for="globalEmailEnabled">
                                <i class="fas fa-envelope mr-2"></i>Email Notifications
                            </label>
                        </div>
                        <div class="custom-control custom-switch channel-toggle">
                            <input type="checkbox" class="custom-control-input" id="globalSmsEnabled">
                            <label class="custom-control-label" for="globalSmsEnabled">
                                <i class="fas fa-sms mr-2"></i>SMS Notifications
                            </label>
                        </div>
                        <div class="custom-control custom-switch channel-toggle">
                            <input type="checkbox" class="custom-control-input" id="globalPushEnabled">
                            <label class="custom-control-label" for="globalPushEnabled">
                                <i class="fas fa-bell mr-2"></i>Push Notifications
                            </label>
                        </div>
                        <div class="custom-control custom-switch channel-toggle">
                            <input type="checkbox" class="custom-control-input" id="globalSoundEnabled">
                            <label class="custom-control-label" for="globalSoundEnabled">
                                <i class="fas fa-volume-up mr-2"></i>Sound Alerts
                            </label>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <h5>Timing Settings</h5>
                    <div class="form-group">
                        <label>Quiet Hours</label>
                        <div class="row">
                            <div class="col-6">
                                <input type="time" class="form-control" id="globalQuietHoursStart" placeholder="Start">
                            </div>
                            <div class="col-6">
                                <input type="time" class="form-control" id="globalQuietHoursEnd" placeholder="End">
                            </div>
                        </div>
                        <small class="text-muted">No notifications during these hours</small>
                    </div>
                    <div class="form-group">
                        <div class="custom-control custom-switch">
                            <input type="checkbox" class="custom-control-input" id="globalWeekendNotifications">
                            <label class="custom-control-label" for="globalWeekendNotifications">
                                Weekend Notifications
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Digest Frequency</label>
                        <select class="form-control" id="globalDigestFrequency">
                            <option value="never">Never</option>
                            <option value="daily">Daily</option>
                            <option value="weekly">Weekly</option>
                            <option value="monthly">Monthly</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="text-center mt-3">
                <button type="button" class="btn btn-light" id="applyGlobalSettingsBtn">
                    <i class="fas fa-magic mr-2"></i>Apply to All Notification Types
                </button>
            </div>
        </div>
    </div>

    
    <div class="row">
        <?php $__currentLoopData = $notificationTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-lg-6 col-xl-4 mb-4">
                <div class="card preference-card notification-type-card <?php echo e($type); ?>" data-type="<?php echo e($type); ?>">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <?php
                                $icons = [
                                    'booking' => 'fas fa-calendar-check',
                                    'cancellation' => 'fas fa-calendar-times',
                                    'payment' => 'fas fa-credit-card',
                                    'review' => 'fas fa-star',
                                    'system' => 'fas fa-cog',
                                    'marketing' => 'fas fa-bullhorn',
                                    'alert' => 'fas fa-exclamation-triangle',
                                    'reminder' => 'fas fa-clock',
                                    'customer_message' => 'fas fa-comment',
                                    'waiting_list' => 'fas fa-list'
                                ];
                            ?>
                            <i class="<?php echo e($icons[$type] ?? 'fas fa-bell'); ?> mr-2"></i>
                            <?php echo e($label); ?>

                        </h5>
                        <div class="card-tools">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input master-toggle"
                                       id="masterToggle<?php echo e(ucfirst($type)); ?>"
                                       data-type="<?php echo e($type); ?>">
                                <label class="custom-control-label" for="masterToggle<?php echo e(ucfirst($type)); ?>"></label>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        
                        <div class="mb-3">
                            <h6>Delivery Channels</h6>
                            <div class="custom-control custom-switch channel-toggle">
                                <input type="checkbox" class="custom-control-input channel-toggle-input"
                                       id="email<?php echo e(ucfirst($type)); ?>"
                                       data-type="<?php echo e($type); ?>"
                                       data-channel="email">
                                <label class="custom-control-label" for="email<?php echo e(ucfirst($type)); ?>">
                                    <i class="fas fa-envelope mr-1"></i>Email
                                </label>
                            </div>
                            <div class="custom-control custom-switch channel-toggle">
                                <input type="checkbox" class="custom-control-input channel-toggle-input"
                                       id="sms<?php echo e(ucfirst($type)); ?>"
                                       data-type="<?php echo e($type); ?>"
                                       data-channel="sms">
                                <label class="custom-control-label" for="sms<?php echo e(ucfirst($type)); ?>">
                                    <i class="fas fa-sms mr-1"></i>SMS
                                </label>
                            </div>
                            <div class="custom-control custom-switch channel-toggle">
                                <input type="checkbox" class="custom-control-input channel-toggle-input"
                                       id="push<?php echo e(ucfirst($type)); ?>"
                                       data-type="<?php echo e($type); ?>"
                                       data-channel="push">
                                <label class="custom-control-label" for="push<?php echo e(ucfirst($type)); ?>">
                                    <i class="fas fa-bell mr-1"></i>Push
                                </label>
                            </div>
                            <div class="custom-control custom-switch channel-toggle">
                                <input type="checkbox" class="custom-control-input channel-toggle-input"
                                       id="inApp<?php echo e(ucfirst($type)); ?>"
                                       data-type="<?php echo e($type); ?>"
                                       data-channel="in_app">
                                <label class="custom-control-label" for="inApp<?php echo e(ucfirst($type)); ?>">
                                    <i class="fas fa-desktop mr-1"></i>In-App
                                </label>
                            </div>
                        </div>

                        
                        <div class="mb-3">
                            <label class="form-label">Minimum Priority</label>
                            <select class="form-control form-control-sm priority-filter" data-type="<?php echo e($type); ?>">
                                <option value="">All Priorities</option>
                                <option value="low">Low and above</option>
                                <option value="normal">Normal and above</option>
                                <option value="high">High and above</option>
                                <option value="urgent">Urgent only</option>
                            </select>
                        </div>

                        
                        <div class="mb-2">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input auto-mark-read"
                                       id="autoMarkRead<?php echo e(ucfirst($type)); ?>"
                                       data-type="<?php echo e($type); ?>">
                                <label class="custom-control-label" for="autoMarkRead<?php echo e(ucfirst($type)); ?>">
                                    Auto-mark as read
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>

    
    <div class="modal fade" id="testNotificationModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">
                        <i class="fas fa-bell mr-2"></i>
                        Test Notification
                    </h4>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label>Notification Type</label>
                        <select class="form-control" id="testNotificationType">
                            <?php $__currentLoopData = $notificationTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($type); ?>"><?php echo e($label); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Test Channels</label>
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="testEmail" value="email">
                            <label class="custom-control-label" for="testEmail">Email</label>
                        </div>
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="testSms" value="sms">
                            <label class="custom-control-label" for="testSms">SMS</label>
                        </div>
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="testPush" value="push">
                            <label class="custom-control-label" for="testPush">Push Notification</label>
                        </div>
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="testInApp" value="in_app" checked>
                            <label class="custom-control-label" for="testInApp">In-App Notification</label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="sendTestNotificationBtn" onclick="sendTestNotification()">
                        <i class="fas fa-paper-plane mr-2"></i>Send Test
                    </button>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
<script>
// Global functions that can be called from onclick attributes
window.fallbackSavePreferences = function() {
    console.log('Fallback save preferences called');

    // Show loading state
    $('#savePreferencesBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Saving...');

    try {
        savePreferences();
    } catch (error) {
        console.error('Error in savePreferences:', error);
        if (typeof toastr !== 'undefined') {
            toastr.error('An error occurred while saving preferences');
        } else {
            alert('An error occurred while saving preferences');
        }
        $('#savePreferencesBtn').prop('disabled', false).html('<i class="fas fa-save"></i> Save Preferences');
    }
};

window.fallbackResetPreferences = function() {
    console.log('Fallback reset preferences called');

    if (confirm('Are you sure you want to reset all preferences to defaults? This action cannot be undone.')) {
        // Show loading state
        $('#resetPreferencesBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Resetting...');

        try {
            resetPreferences();
        } catch (error) {
            console.error('Error in resetPreferences:', error);
            if (typeof toastr !== 'undefined') {
                toastr.error('An error occurred while resetting preferences');
            } else {
                alert('An error occurred while resetting preferences');
            }
            $('#resetPreferencesBtn').prop('disabled', false).html('<i class="fas fa-undo"></i> Reset to Defaults');
        }
    }
};

window.fallbackTestNotification = function() {
    console.log('Fallback test notification called');

    try {
        $('#testNotificationModal').modal('show');
    } catch (error) {
        console.error('Error opening test modal:', error);
        if (typeof toastr !== 'undefined') {
            toastr.error('An error occurred while opening test modal');
        } else {
            alert('An error occurred while opening test modal');
        }
    }
};

// Simple test function to verify buttons are working
window.testButtonFunctionality = function() {
    console.log('Testing button functionality...');

    if (typeof $ === 'undefined') {
        alert('jQuery is not loaded!');
        return false;
    }

    if ($('#savePreferencesBtn').length === 0) {
        alert('Save button not found!');
        return false;
    }

    if ($('#resetPreferencesBtn').length === 0) {
        alert('Reset button not found!');
        return false;
    }

    if ($('#testNotificationBtn').length === 0) {
        alert('Test button not found!');
        return false;
    }

    alert('All buttons found and jQuery is working!');
    return true;
};

window.sendTestNotification = function() {
    console.log('Send test notification called');

    const type = $('#testNotificationType').val();
    const channels = [];

    $('#testNotificationModal input[type="checkbox"]:checked').each(function() {
        channels.push($(this).val());
    });

    if (channels.length === 0) {
        if (typeof toastr !== 'undefined') {
            toastr.warning('Please select at least one channel to test');
        } else {
            alert('Please select at least one channel to test');
        }
        return;
    }

    // Show loading state
    $('#sendTestNotificationBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Sending...');

    $.ajax({
        url: '<?php echo e(route("owner.notification-preferences.test")); ?>',
        type: 'POST',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        data: {
            notification_type: type,
            channels: channels
        },
        success: function(response) {
            if (response.success) {
                if (typeof toastr !== 'undefined') {
                    toastr.success(response.message || 'Test notification sent successfully!');
                } else {
                    alert('Test notification sent successfully!');
                }
                $('#testNotificationModal').modal('hide');
            } else {
                if (typeof toastr !== 'undefined') {
                    toastr.error(response.message || 'Failed to send test notification');
                } else {
                    alert('Failed to send test notification');
                }
            }
        },
        error: function(xhr) {
            let errorMessage = 'Failed to send test notification';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }
            if (typeof toastr !== 'undefined') {
                toastr.error(errorMessage);
            } else {
                alert(errorMessage);
            }
            console.error('Test notification error:', xhr);
        },
        complete: function() {
            // Reset button state
            $('#sendTestNotificationBtn').prop('disabled', false).html('<i class="fas fa-paper-plane mr-2"></i>Send Test');
        }
    });
};

// Core functions that need to be globally accessible
window.savePreferences = function() {
    const preferences = [];

    $('.notification-type-card').each(function() {
        const type = $(this).data('type');
        const typeCapitalized = type.charAt(0).toUpperCase() + type.slice(1);

        preferences.push({
            notification_type: type,
            email_enabled: $(`#email${typeCapitalized}`).is(':checked'),
            sms_enabled: $(`#sms${typeCapitalized}`).is(':checked'),
            push_enabled: $(`#push${typeCapitalized}`).is(':checked'),
            in_app_enabled: $(`#inApp${typeCapitalized}`).is(':checked'),
            sound_enabled: true, // Always enabled for now
            priority_filter: $(`.priority-filter[data-type="${type}"]`).val() || null,
            quiet_hours_start: null, // Global setting for now
            quiet_hours_end: null, // Global setting for now
            weekend_notifications: true, // Global setting for now
            digest_frequency: 'daily', // Global setting for now
            auto_mark_read: $(`#autoMarkRead${typeCapitalized}`).is(':checked')
        });
    });

    $.ajax({
        url: '<?php echo e(route("owner.notification-preferences.update")); ?>',
        type: 'POST',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        data: { preferences: preferences },
        success: function(response) {
            if (response.success) {
                if (typeof toastr !== 'undefined') {
                    toastr.success(response.message || 'Preferences saved successfully!');
                } else {
                    alert('Preferences saved successfully!');
                }
            } else {
                if (typeof toastr !== 'undefined') {
                    toastr.error(response.message || 'Failed to save preferences');
                } else {
                    alert('Failed to save preferences');
                }
            }
        },
        error: function(xhr) {
            let errorMessage = 'Failed to save preferences';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                errorMessage = Object.values(xhr.responseJSON.errors).flat().join(', ');
            }
            if (typeof toastr !== 'undefined') {
                toastr.error(errorMessage);
            } else {
                alert(errorMessage);
            }
            console.error('Save preferences error:', xhr);
        },
        complete: function() {
            // Reset button state
            $('#savePreferencesBtn').prop('disabled', false).html('<i class="fas fa-save"></i> Save Preferences');
        }
    });
};

window.resetPreferences = function() {
    $.ajax({
        url: '<?php echo e(route("owner.notification-preferences.reset")); ?>',
        type: 'POST',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                if (typeof toastr !== 'undefined') {
                    toastr.success(response.message || 'Preferences reset to defaults successfully!');
                } else {
                    alert('Preferences reset to defaults successfully!');
                }
                // Reload the page after a short delay to show the success message
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                if (typeof toastr !== 'undefined') {
                    toastr.error(response.message || 'Failed to reset preferences');
                } else {
                    alert('Failed to reset preferences');
                }
            }
        },
        error: function(xhr) {
            let errorMessage = 'Failed to reset preferences';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }
            if (typeof toastr !== 'undefined') {
                toastr.error(errorMessage);
            } else {
                alert(errorMessage);
            }
            console.error('Reset preferences error:', xhr);
        },
        complete: function() {
            // Reset button state
            $('#resetPreferencesBtn').prop('disabled', false).html('<i class="fas fa-undo"></i> Reset to Defaults');
        }
    });
};

$(document).ready(function() {
    // CSRF token setup
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Debug: Check if jQuery and required elements are loaded
    console.log('jQuery loaded:', typeof $ !== 'undefined');
    console.log('CSRF token:', $('meta[name="csrf-token"]').attr('content'));
    console.log('Save button exists:', $('#savePreferencesBtn').length > 0);
    console.log('Reset button exists:', $('#resetPreferencesBtn').length > 0);
    console.log('Test button exists:', $('#testNotificationBtn').length > 0);
    console.log('Toastr available:', typeof toastr !== 'undefined');

    // Test if buttons are clickable
    if ($('#savePreferencesBtn').length > 0) {
        console.log('Save button found, adding test click handler');
    } else {
        console.error('Save button not found!');
    }

    // Add immediate click test for debugging
    $('#savePreferencesBtn, #resetPreferencesBtn, #testNotificationBtn').on('click', function() {
        console.log('Button clicked via jQuery:', this.id);
    });

    // Load current preferences
    loadPreferences();

    // Master toggle for each notification type
    $('.master-toggle').change(function() {
        const type = $(this).data('type');
        const enabled = $(this).is(':checked');
        const card = $(`.notification-type-card[data-type="${type}"]`);

        // Toggle all channels for this type
        card.find('.channel-toggle-input').prop('checked', enabled);

        // Enable/disable the card
        card.toggleClass('disabled', !enabled);
        card.find('input, select').not('.master-toggle').prop('disabled', !enabled);
    });

    // Apply global settings to all types
    $('#applyGlobalSettingsBtn').click(function() {
        const globalSettings = {
            email: $('#globalEmailEnabled').is(':checked'),
            sms: $('#globalSmsEnabled').is(':checked'),
            push: $('#globalPushEnabled').is(':checked'),
            sound: $('#globalSoundEnabled').is(':checked'),
            quietStart: $('#globalQuietHoursStart').val(),
            quietEnd: $('#globalQuietHoursEnd').val(),
            weekend: $('#globalWeekendNotifications').is(':checked'),
            digest: $('#globalDigestFrequency').val()
        };

        // Apply to all notification types
        $('.notification-type-card').each(function() {
            const type = $(this).data('type');
            $(this).find(`#email${type.charAt(0).toUpperCase() + type.slice(1)}`).prop('checked', globalSettings.email);
            $(this).find(`#sms${type.charAt(0).toUpperCase() + type.slice(1)}`).prop('checked', globalSettings.sms);
            $(this).find(`#push${type.charAt(0).toUpperCase() + type.slice(1)}`).prop('checked', globalSettings.push);
            $(this).find(`#inApp${type.charAt(0).toUpperCase() + type.slice(1)}`).prop('checked', true); // Always enable in-app
        });

        toastr.success('Global settings applied to all notification types');
    });

    // Save preferences
    $('#savePreferencesBtn').click(function(e) {
        e.preventDefault();
        console.log('Save preferences button clicked');

        // Show loading state
        $(this).prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Saving...');

        try {
            savePreferences();
        } catch (error) {
            console.error('Error in savePreferences:', error);
            toastr.error('An error occurred while saving preferences');
            $(this).prop('disabled', false).html('<i class="fas fa-save"></i> Save Preferences');
        }
    });

    // Reset preferences
    $('#resetPreferencesBtn').click(function(e) {
        e.preventDefault();
        console.log('Reset preferences button clicked');

        if (confirm('Are you sure you want to reset all preferences to defaults? This action cannot be undone.')) {
            // Show loading state
            $(this).prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Resetting...');

            try {
                resetPreferences();
            } catch (error) {
                console.error('Error in resetPreferences:', error);
                toastr.error('An error occurred while resetting preferences');
                $(this).prop('disabled', false).html('<i class="fas fa-undo"></i> Reset to Defaults');
            }
        }
    });

    // Test notification
    $('#testNotificationBtn').click(function(e) {
        e.preventDefault();
        console.log('Test notification button clicked');
        $('#testNotificationModal').modal('show');
    });

    // Send test notification
    $('#sendTestNotificationBtn').click(function(e) {
        e.preventDefault();
        console.log('Send test notification button clicked');

        // Show loading state
        $(this).prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Sending...');

        try {
            sendTestNotification();
        } catch (error) {
            console.error('Error in sendTestNotification:', error);
            toastr.error('An error occurred while sending test notification');
            $(this).prop('disabled', false).html('<i class="fas fa-paper-plane"></i> Send Test');
        }
    });

    function loadPreferences() {
        // Load preferences from server-side data
        <?php $__currentLoopData = $preferences; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $preference): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            const type<?php echo e(ucfirst($preference->notification_type)); ?> = <?php echo json_encode($preference, 15, 512) ?>;
            loadPreferenceData('<?php echo e($preference->notification_type); ?>', type<?php echo e(ucfirst($preference->notification_type)); ?>);
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    }

    function loadPreferenceData(type, data) {
        const typeCapitalized = type.charAt(0).toUpperCase() + type.slice(1);

        // Set channel toggles
        $(`#email${typeCapitalized}`).prop('checked', data.email_enabled);
        $(`#sms${typeCapitalized}`).prop('checked', data.sms_enabled);
        $(`#push${typeCapitalized}`).prop('checked', data.push_enabled);
        $(`#inApp${typeCapitalized}`).prop('checked', data.in_app_enabled);

        // Set priority filter
        $(`.priority-filter[data-type="${type}"]`).val(data.priority_filter || '');

        // Set auto-mark read
        $(`#autoMarkRead${typeCapitalized}`).prop('checked', data.auto_mark_read);

        // Set master toggle based on whether any channel is enabled
        const anyEnabled = data.email_enabled || data.sms_enabled || data.push_enabled || data.in_app_enabled;
        $(`#masterToggle${typeCapitalized}`).prop('checked', anyEnabled);

        // Trigger master toggle to set card state
        $(`#masterToggle${typeCapitalized}`).trigger('change');
    }

    // These functions are now defined globally above
});

// Ensure functions are available even if jQuery fails to load
window.onload = function() {
    if (typeof window.fallbackSavePreferences === 'undefined') {
        window.fallbackSavePreferences = function() {
            alert('JavaScript not fully loaded. Please refresh the page and try again.');
        };
    }

    if (typeof window.fallbackResetPreferences === 'undefined') {
        window.fallbackResetPreferences = function() {
            alert('JavaScript not fully loaded. Please refresh the page and try again.');
        };
    }

    if (typeof window.fallbackTestNotification === 'undefined') {
        window.fallbackTestNotification = function() {
            alert('JavaScript not fully loaded. Please refresh the page and try again.');
        };
    }
};

// Helper function to get notification type icon
function getTypeIcon(type) {
    const icons = {
        'booking': 'fas fa-calendar-check',
        'cancellation': 'fas fa-calendar-times',
        'payment': 'fas fa-credit-card',
        'review': 'fas fa-star',
        'system': 'fas fa-cog',
        'marketing': 'fas fa-bullhorn',
        'alert': 'fas fa-exclamation-triangle',
        'reminder': 'fas fa-clock',
        'customer_message': 'fas fa-comment',
        'waiting_list': 'fas fa-list'
    };
    return icons[type] || 'fas fa-bell';
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('owner.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\bookkei\resources\views/owner/notifications/preferences.blade.php ENDPATH**/ ?>